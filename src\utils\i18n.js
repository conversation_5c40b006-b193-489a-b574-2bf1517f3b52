// i18next configuration and initialization
// Using UMD builds for compatibility with Electron renderer process

class I18nManager {
    constructor() {
        this.isInitialized = false;
        this.currentLanguage = 'en';
        this.supportedLanguages = ['en', 'es', 'fr', 'de', 'zh', 'ja'];
        this.languageNames = {
            'en': 'English',
            'es': 'Español',
            'fr': 'Français',
            'de': 'Deutsch',
            'zh': '中文',
            'ja': '日本語'
        };
    }

    async init() {
        if (this.isInitialized) {
            return window.i18next;
        }

        // Check if i18next is available globally
        if (!window.i18next) {
            throw new Error('i18next is not loaded. Please include i18next scripts in your HTML.');
        }

        try {
            await window.i18next
                .use(window.i18nextHttpBackend)
                .use(window.i18nextBrowserLanguageDetector)
                .init({
                    // Language detection options
                    detection: {
                        order: ['localStorage', 'navigator', 'htmlTag'],
                        lookupLocalStorage: 'i18nextLng',
                        caches: ['localStorage'],
                        excludeCacheFor: ['cimode']
                    },

                    // Backend options
                    backend: {
                        loadPath: './locales/{{lng}}/{{ns}}.json',
                        addPath: './locales/{{lng}}/{{ns}}.missing.json'
                    },

                    // Language options
                    lng: this.currentLanguage,
                    fallbackLng: 'en',
                    supportedLngs: this.supportedLanguages,
                    
                    // Namespace options
                    ns: ['translation'],
                    defaultNS: 'translation',

                    // Debug options
                    debug: false,

                    // Interpolation options
                    interpolation: {
                        escapeValue: false // React already does escaping
                    },

                    // React options (if using React in the future)
                    react: {
                        useSuspense: false
                    }
                });

            this.isInitialized = true;
            this.currentLanguage = window.i18next.language;

            // Set up language change listener
            window.i18next.on('languageChanged', (lng) => {
                this.currentLanguage = lng;
                this.updatePageLanguage();
                this.saveLanguagePreference(lng);
            });

            console.log('i18next initialized successfully');
            return window.i18next;
        } catch (error) {
            console.error('Failed to initialize i18next:', error);
            throw error;
        }
    }

    // Get translation function
    t(key, options = {}) {
        if (!this.isInitialized) {
            console.warn('i18next not initialized, returning key:', key);
            return key;
        }
        return window.i18next.t(key, options);
    }

    // Change language
    async changeLanguage(lng) {
        if (!this.supportedLanguages.includes(lng)) {
            console.warn(`Language ${lng} is not supported`);
            return false;
        }

        try {
            await window.i18next.changeLanguage(lng);
            return true;
        } catch (error) {
            console.error('Failed to change language:', error);
            return false;
        }
    }

    // Get current language
    getCurrentLanguage() {
        return this.currentLanguage;
    }

    // Get supported languages
    getSupportedLanguages() {
        return this.supportedLanguages.map(code => ({
            code,
            name: this.languageNames[code] || code
        }));
    }

    // Update page language attributes and content
    updatePageLanguage() {
        // Update HTML lang attribute
        document.documentElement.lang = this.currentLanguage;
        
        // Update all elements with data-i18n attributes
        this.updateTranslatedElements();
        
        // Trigger custom event for components to update
        window.dispatchEvent(new CustomEvent('languageChanged', {
            detail: { language: this.currentLanguage }
        }));
    }

    // Update all elements with translation attributes
    updateTranslatedElements() {
        // Update elements with data-i18n attribute
        const elements = document.querySelectorAll('[data-i18n]');
        elements.forEach(element => {
            const key = element.getAttribute('data-i18n');
            if (key) {
                element.textContent = this.t(key);
            }
        });

        // Update elements with data-i18n-placeholder attribute
        const placeholderElements = document.querySelectorAll('[data-i18n-placeholder]');
        placeholderElements.forEach(element => {
            const key = element.getAttribute('data-i18n-placeholder');
            if (key) {
                element.placeholder = this.t(key);
            }
        });

        // Update elements with data-i18n-title attribute
        const titleElements = document.querySelectorAll('[data-i18n-title]');
        titleElements.forEach(element => {
            const key = element.getAttribute('data-i18n-title');
            if (key) {
                element.title = this.t(key);
            }
        });

        // Update elements with data-i18n-html attribute (for HTML content)
        const htmlElements = document.querySelectorAll('[data-i18n-html]');
        htmlElements.forEach(element => {
            const key = element.getAttribute('data-i18n-html');
            if (key) {
                element.innerHTML = this.t(key);
            }
        });
    }

    // Save language preference to settings
    async saveLanguagePreference(lng) {
        try {
            if (window.electronAPI && window.electronAPI.saveSettings) {
                const settings = await window.electronAPI.getSettings() || {};
                settings.language = lng;
                await window.electronAPI.saveSettings(settings);
            }
        } catch (error) {
            console.error('Failed to save language preference:', error);
        }
    }

    // Load language preference from settings
    async loadLanguagePreference() {
        try {
            if (window.electronAPI && window.electronAPI.getSettings) {
                const settings = await window.electronAPI.getSettings() || {};
                if (settings.language && this.supportedLanguages.includes(settings.language)) {
                    await this.changeLanguage(settings.language);
                }
            }
        } catch (error) {
            console.error('Failed to load language preference:', error);
        }
    }

    // Get language direction (for RTL languages in the future)
    getLanguageDirection(lng = this.currentLanguage) {
        const rtlLanguages = ['ar', 'he', 'fa', 'ur'];
        return rtlLanguages.includes(lng) ? 'rtl' : 'ltr';
    }

    // Format date according to current language
    formatDate(date, options = {}) {
        const defaultOptions = {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        };
        
        return new Intl.DateTimeFormat(this.currentLanguage, { ...defaultOptions, ...options }).format(date);
    }

    // Format number according to current language
    formatNumber(number, options = {}) {
        return new Intl.NumberFormat(this.currentLanguage, options).format(number);
    }

    // Format file size with localized units
    formatFileSize(bytes) {
        const units = ['bytes', 'KB', 'MB', 'GB', 'TB'];
        let size = bytes;
        let unitIndex = 0;

        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }

        const formattedSize = this.formatNumber(size, { 
            maximumFractionDigits: unitIndex === 0 ? 0 : 1 
        });
        
        return `${formattedSize} ${units[unitIndex]}`;
    }
}

// Create and expose singleton instance globally
const i18nManager = new I18nManager();

// Make it available globally for the application
window.i18nManager = i18nManager;

// Convenience functions
window.t = (key, options) => i18nManager.t(key, options);
window.changeLanguage = (lng) => i18nManager.changeLanguage(lng);
window.getCurrentLanguage = () => i18nManager.getCurrentLanguage();
window.getSupportedLanguages = () => i18nManager.getSupportedLanguages();
