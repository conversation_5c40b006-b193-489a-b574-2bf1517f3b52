{"app": {"title": "Downloader Pro", "version": "Version {{version}}"}, "header": {"toggleTheme": "Toggle Theme", "settings": "Settings"}, "input": {"urlPlaceholder": "Paste video URL here...", "pasteFromClipboard": "Paste from clipboard", "autoDetectClipboard": "Auto-detect clipboard", "download": "Download", "scheduleDownload": "Schedule Download", "notificationSettings": "Notification Settings", "queueManagement": "Queue Management"}, "platform": {"header": "Find videos to download right in the app", "description": "Tap the service icon below and start searching", "youtube": "YouTube", "facebook": "Facebook", "instagram": "Instagram", "tiktok": "TikTok", "twitter": "Twitter"}, "preview": {"title": "Video Preview", "uploader": "Uploader", "views": "Views", "duration": "Duration"}, "downloadOptions": {"downloadType": "Download Type", "quality": "Quality/Resolution", "format": "Output Format", "destination": "Destination", "browse": "Browse for folder", "types": {"video": "Video", "audio": "Audio Only", "subtitles": "Subtitles Only", "audioTracks": "Audio Tracks"}, "qualities": {"best": "Best Available", "8k": "UHD 8K", "4k": "HD 4K", "2k": "HQ 2K", "1080p": "1080p", "720p": "720p", "480p": "480p", "360p": "360p", "240p": "240p", "worst": "Lowest Quality"}, "formats": {"auto": "Auto", "mp4": "MP4", "mkv": "MKV", "webm": "WebM", "mp3": "MP3 (Audio)", "aac": "AAC (Audio)", "ogg": "OGG (Audio)"}, "destinations": {"downloads": "Downloads", "videos": "Videos", "music": "Music", "documents": "Documents", "custom": "Browse..."}}, "downloads": {"title": "Downloads", "clearCompleted": "Clear Completed", "pauseAll": "Pause All", "history": "History", "empty": {"title": "No downloads yet", "description": "Paste a video URL above to get started"}, "status": {"pending": "Pending", "downloading": "Downloading", "completed": "Completed", "failed": "Failed", "paused": "Paused", "cancelled": "Cancelled"}, "actions": {"pause": "Pause", "resume": "Resume", "cancel": "Cancel", "retry": "Retry", "remove": "Remove", "openFile": "Open File", "openFolder": "Open Folder", "copyUrl": "Copy URL"}}, "settings": {"title": "Settings", "general": "General", "downloads": "Downloads", "advanced": "Advanced", "about": "About", "language": "Language", "theme": "Theme", "autoClipboard": "Auto-detect clipboard URLs", "defaultQuality": "Default Quality", "defaultFormat": "Default Format", "defaultDestination": "Default Destination", "maxConcurrentDownloads": "Max Concurrent Downloads", "downloadTimeout": "Download Timeout (seconds)", "retryAttempts": "Retry Attempts", "enableNotifications": "Enable Notifications", "soundNotifications": "Sound Notifications", "save": "Save", "cancel": "Cancel", "reset": "Reset to Defaults"}, "history": {"title": "Download History", "clearHistory": "Clear History", "export": "Export", "empty": {"title": "No download history yet", "description": "Completed downloads will appear here"}, "columns": {"title": "Title", "url": "URL", "date": "Date", "size": "Size", "duration": "Duration", "status": "Status"}}, "about": {"title": "About Downloader Pro", "description": "Modern desktop application for downloading content from various platforms", "githubRepo": "GitHub Repository", "reportIssues": "Report Issues"}, "notifications": {"downloadStarted": "Download started", "downloadCompleted": "Download completed: {{title}}", "downloadFailed": "Download failed: {{error}}", "urlCopied": "URL copied to clipboard", "settingsSaved": "Setting<PERSON> saved successfully", "historyCleared": "Download history cleared", "historyExported": "History exported successfully"}, "errors": {"invalidUrl": "Please enter a valid URL", "networkError": "Network error occurred", "downloadError": "Download failed", "fileNotFound": "File not found", "permissionDenied": "Permission denied", "diskSpaceLow": "Insufficient disk space", "unsupportedFormat": "Unsupported format", "timeout": "Request timeout"}, "common": {"ok": "OK", "cancel": "Cancel", "yes": "Yes", "no": "No", "close": "Close", "save": "Save", "delete": "Delete", "edit": "Edit", "copy": "Copy", "paste": "Paste", "loading": "Loading...", "retry": "Retry", "refresh": "Refresh"}}