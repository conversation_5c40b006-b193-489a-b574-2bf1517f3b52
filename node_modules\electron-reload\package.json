{"name": "electron-reload", "version": "2.0.0-alpha.1", "description": "Simplest way to reload an electron app on file changes!", "main": "main.js", "scripts": {"lint": "standard", "lint:fix": "standard --fix"}, "repository": {"type": "git", "url": "https://github.com/yan-foto/electron-reload.git"}, "keywords": ["electron", "reload", "auto-reload", "node"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/yan-foto/electron-reload/issues"}, "dependencies": {"chokidar": "^3.5.2"}, "devDependencies": {"standard": "^13.1.0"}, "types": "./types/main.d.ts"}