/* Button Components */
.icon-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 8px;
    background-color: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
}

.icon-button:hover {
    background-color: var(--bg-hover);
    color: var(--text-primary);
}

.download-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border: none;
    border-radius: 8px;
    background: linear-gradient(135deg, var(--accent-color), var(--accent-hover));
    color: white;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.download-button:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.download-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.secondary-button {
    padding: 0.5rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: 6px;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.secondary-button:hover {
    border-color: var(--accent-color);
    background-color: var(--bg-hover);
}

.link-button {
    background: none;
    border: none;
    color: var(--accent-color);
    text-decoration: underline;
    cursor: pointer;
    font-size: 0.9rem;
    padding: 0.25rem 0;
    transition: color 0.2s ease;
}

.link-button:hover {
    color: var(--accent-hover);
}

/* Toggle Switch */
.toggle-switch {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    user-select: none;
}

.toggle-switch input {
    display: none;
}

.slider {
    position: relative;
    width: 44px;
    height: 24px;
    background-color: var(--border-color);
    border-radius: 24px;
    transition: background-color 0.2s ease;
}

.slider:before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background-color: white;
    border-radius: 50%;
    transition: transform 0.2s ease;
}

.toggle-switch input:checked + .slider {
    background-color: var(--accent-color);
}

.toggle-switch input:checked + .slider:before {
    transform: translateX(20px);
}

.toggle-label {
    font-size: 0.9rem;
    color: var(--text-primary);
}

/* Progress Bar */
.progress-bar {
    width: 100%;
    height: 8px;
    background-color: var(--bg-primary);
    border-radius: 4px;
    overflow: hidden;
    margin: 0.5rem 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--accent-color), var(--accent-hover));
    border-radius: 4px;
    transition: width 0.3s ease;
    width: 0%;
}

/* Download Item */
.download-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    transition: background-color 0.2s ease;
}

.download-item:hover {
    background-color: var(--bg-hover);
}

.download-item:last-child {
    border-bottom: none;
}

.download-thumbnail {
    width: 80px;
    height: 45px;
    border-radius: 4px;
    object-fit: cover;
    flex-shrink: 0;
}

.download-info {
    flex: 1;
    min-width: 0;
}

.download-title {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.download-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.download-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.8rem;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.status-downloading {
    background-color: var(--accent-color);
    animation: pulse 2s infinite;
}

.status-completed {
    background-color: #10b981;
}

.status-error {
    background-color: #ef4444;
}

.status-paused {
    background-color: #f59e0b;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.download-controls {
    display: flex;
    gap: 0.25rem;
}

.control-button {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 4px;
    background-color: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.control-button:hover {
    background-color: var(--bg-hover);
    color: var(--text-primary);
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background-color: var(--bg-secondary);
    border-radius: 12px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
    color: var(--text-primary);
    font-size: 1.3rem;
}

.modal-close {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 4px;
    background-color: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 1.5rem;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background-color: var(--bg-hover);
    color: var(--text-primary);
}

.modal-body {
    padding: 1.5rem;
    max-height: 60vh;
    overflow-y: auto;
}

/* About Modal */
.about-content {
    text-align: center;
}

.about-icon {
    width: 64px;
    height: 64px;
    margin-bottom: 1rem;
}

.about-content h3 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.about-content p {
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.about-links {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-top: 1.5rem;
}

/* Notification */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1001;
    max-width: 300px;
    animation: slideIn 0.3s ease;
}

.notification.success {
    border-left: 4px solid #10b981;
}

.notification.error {
    border-left: 4px solid #ef4444;
}

.notification.info {
    border-left: 4px solid var(--accent-color);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.notification-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.notification-message {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* Loading Spinner */
.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--accent-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Platform Search Modal */
.platform-search-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.platform-search-modal .modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.platform-search-content {
    position: relative;
    background: var(--bg-primary);
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
    border: 1px solid var(--border-color);
}

.platform-search-content .modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.platform-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.platform-modal-icon {
    width: 32px;
    height: 32px;
    border-radius: 8px;
}

.platform-info h2 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.25rem;
    font-weight: 600;
}

.platform-search-content .modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.platform-search-content .modal-close:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.platform-search-content .modal-body {
    padding: 1.5rem;
}

.search-input-container {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.platform-search-input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 1rem;
    transition: all 0.2s ease;
}

.platform-search-input:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.platform-search-button {
    padding: 0.75rem 1.5rem;
    background: var(--accent-color);
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.platform-search-button:hover {
    background: var(--accent-hover);
    transform: translateY(-1px);
}

.search-tips, .search-workflow {
    margin-bottom: 1.5rem;
}

.search-tips h4, .search-workflow h4 {
    margin: 0 0 0.75rem 0;
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 600;
}

.search-tips ul, .search-workflow ol {
    margin: 0;
    padding-left: 1.25rem;
    color: var(--text-secondary);
}

.search-tips li, .search-workflow li {
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.platform-search-content .modal-footer {
    display: flex;
    gap: 0.75rem;
    justify-content: flex-end;
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
    background: var(--bg-secondary);
    border-radius: 0 0 16px 16px;
}

.btn-secondary, .btn-primary {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-secondary {
    background: var(--bg-primary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.btn-primary {
    background: var(--accent-color);
    color: white;
}

.btn-primary:hover {
    background: var(--accent-hover);
    transform: translateY(-1px);
}

/* Responsive design for platform search modal */
@media (max-width: 768px) {
    .platform-search-content {
        width: 95%;
        margin: 1rem;
    }

    .search-input-container {
        flex-direction: column;
    }

    .platform-search-content .modal-footer {
        flex-direction: column;
    }

    .btn-secondary, .btn-primary {
        width: 100%;
    }
}

/* Drag and Drop Styles */
.drag-over {
    position: relative;
    background: linear-gradient(135deg, var(--primary-color)10, var(--primary-color)20);
    border: 2px dashed var(--primary-color);
    border-radius: 12px;
}

.drag-over::before {
    content: '📁 Drop URLs or text files here';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--primary-color);
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 16px;
    z-index: 1000;
    pointer-events: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.drag-over::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 12px;
    z-index: 999;
    pointer-events: none;
}

.theme-dark .drag-over::after {
    background: rgba(0, 0, 0, 0.8);
}

/* Smart Suggestions Panel */
.smart-suggestions-panel {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.smart-suggestions-panel.hidden {
    display: none;
}

.suggestions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--border-color);
}

.suggestions-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 18px;
    font-weight: 600;
}

.confidence-indicator {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.confidence-indicator.high {
    background: #10b981;
    color: white;
}

.confidence-indicator.medium {
    background: #f59e0b;
    color: white;
}

.confidence-indicator.low {
    background: #ef4444;
    color: white;
}

.suggested-settings {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 12px;
    margin-bottom: 16px;
    padding: 16px;
    background: var(--bg-primary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.setting-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.setting-label {
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 500;
    text-transform: uppercase;
}

.setting-value {
    font-size: 14px;
    color: var(--text-primary);
    font-weight: 600;
    padding: 4px 8px;
    background: var(--accent-color);
    color: white;
    border-radius: 4px;
    text-align: center;
}

.reasoning {
    margin-bottom: 20px;
}

.reasoning h4 {
    margin: 0 0 8px 0;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
}

.reasoning ul {
    margin: 0;
    padding-left: 20px;
    color: var(--text-secondary);
}

.reasoning li {
    margin-bottom: 4px;
    font-size: 13px;
    line-height: 1.4;
}

.suggestion-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.suggestion-actions .btn-primary,
.suggestion-actions .btn-secondary {
    padding: 8px 16px;
    font-size: 14px;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s ease;
}

.suggestion-actions .btn-primary {
    background: var(--accent-color);
    color: white;
}

.suggestion-actions .btn-primary:hover {
    background: var(--accent-hover);
    transform: translateY(-1px);
}

.suggestion-actions .btn-secondary {
    background: var(--bg-primary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.suggestion-actions .btn-secondary:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

@media (max-width: 768px) {
    .suggested-settings {
        grid-template-columns: 1fr;
    }

    .suggestion-actions {
        flex-direction: column;
    }

    .suggestion-actions .btn-primary,
    .suggestion-actions .btn-secondary {
        width: 100%;
    }
}

/* Quick Actions Menu */
.quick-action-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-secondary);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
    margin-left: 8px;
}

.quick-action-button:hover {
    background: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
    transform: translateY(-1px);
}

.quick-actions-menu {
    position: fixed;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    min-width: 250px;
    overflow: hidden;
}

.quick-actions-menu.hidden {
    display: none;
}

.quick-actions-header {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
}

.quick-actions-list {
    padding: 4px 0;
}

.quick-action-item {
    display: flex;
    align-items: center;
    gap: 12px;
    width: 100%;
    padding: 10px 16px;
    border: none;
    background: transparent;
    color: var(--text-primary);
    cursor: pointer;
    transition: background-color 0.2s ease;
    font-size: 14px;
    text-align: left;
}

.quick-action-item:hover {
    background: var(--bg-hover);
}

.quick-action-item .icon {
    width: 20px;
    text-align: center;
    font-size: 16px;
}

.quick-action-item .label {
    flex: 1;
}

.quick-action-item .shortcut {
    font-size: 12px;
    color: var(--text-secondary);
    background: var(--bg-secondary);
    padding: 2px 6px;
    border-radius: 4px;
    font-family: monospace;
}

.quick-actions-separator {
    height: 1px;
    background: var(--border-color);
    margin: 4px 0;
}

/* Quick Actions Integration with Input Controls */
.input-controls {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

.input-controls .download-button {
    flex: 0 0 auto;
}

.input-controls .quick-action-button {
    flex: 0 0 auto;
}

@media (max-width: 768px) {
    .quick-actions-menu {
        min-width: 200px;
        max-width: 90vw;
    }

    .quick-action-item {
        padding: 12px 16px;
    }

    .quick-action-item .shortcut {
        display: none;
    }
}

/* Keyboard Shortcuts Help */
.shortcuts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
    max-height: 60vh;
    overflow-y: auto;
}

.shortcuts-section {
    background: var(--bg-secondary);
    border-radius: 8px;
    padding: 16px;
}

.shortcuts-section h3 {
    margin: 0 0 12px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 8px;
}

.shortcut-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid var(--border-color);
}

.shortcut-item:last-child {
    border-bottom: none;
}

.shortcut-item kbd {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 2px 6px;
    font-family: monospace;
    font-size: 12px;
    color: var(--text-primary);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    margin: 0 2px;
}

.shortcut-item span {
    color: var(--text-secondary);
    font-size: 14px;
    margin-left: 12px;
}

/* Download item focus state for keyboard navigation */
.download-item.focused {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
    border-radius: 8px;
}

/* Accessibility improvements */
.download-item:focus-within {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

.quick-action-item:focus {
    outline: 2px solid var(--accent-color);
    outline-offset: -2px;
}

/* Keyboard shortcut indicators in UI */
.shortcut-hint {
    font-size: 11px;
    color: var(--text-secondary);
    background: var(--bg-secondary);
    padding: 2px 4px;
    border-radius: 3px;
    margin-left: 8px;
    font-family: monospace;
}

@media (max-width: 768px) {
    .shortcuts-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .shortcut-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }

    .shortcut-item span {
        margin-left: 0;
    }
}

/* Download Scheduler Styles */
.schedule-button {
    background: var(--accent-color);
    border: none;
    border-radius: 8px;
    padding: 12px;
    color: white;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;
    margin-left: 8px;
    min-width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.schedule-button:hover {
    background: var(--accent-hover);
    transform: translateY(-2px);
}

.schedule-button:disabled {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    cursor: not-allowed;
    transform: none;
}

.scheduler-form {
    max-width: 500px;
}

.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 4px;
    font-weight: 500;
    color: var(--text-primary);
}

.form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 14px;
}

.form-control:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(var(--accent-rgb), 0.2);
}

.scheduled-downloads-list {
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid var(--border-color);
}

.scheduled-downloads-list h3 {
    margin: 0 0 12px 0;
    color: var(--text-primary);
    font-size: 16px;
}

.scheduled-item {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 8px;
}

.scheduled-item-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
}

.priority-icon {
    font-size: 12px;
    margin-right: 8px;
}

.scheduled-title {
    flex: 1;
    font-weight: 500;
    color: var(--text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.remove-scheduled {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 18px;
    padding: 0;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.remove-scheduled:hover {
    background: var(--error-color);
    color: white;
}

.scheduled-item-details {
    font-size: 12px;
    color: var(--text-secondary);
}

.scheduled-time {
    margin-bottom: 4px;
}

.recurring-info {
    color: var(--accent-color);
    font-weight: 500;
    margin-bottom: 4px;
}

.scheduled-notes {
    font-style: italic;
    margin-top: 4px;
}

.no-scheduled {
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
    padding: 16px;
}

.hidden {
    display: none !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .schedule-button {
        padding: 10px;
        min-width: 44px;
        height: 44px;
        font-size: 14px;
    }

    .scheduled-item-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .scheduled-title {
        white-space: normal;
        overflow: visible;
        text-overflow: unset;
    }
}

/* Download Templates Styles */
.template-controls {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
}

.template-select {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 14px;
}

.template-select:focus {
    outline: none;
    border-color: var(--accent-color);
}

.manage-templates-btn {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 8px 12px;
    color: var(--text-primary);
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;
}

.manage-templates-btn:hover {
    background: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

.templates-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    max-height: 70vh;
    overflow-y: auto;
}

.create-template-section,
.templates-list-section {
    min-height: 400px;
}

.create-template-section h3,
.templates-list-section h3 {
    margin: 0 0 16px 0;
    color: var(--text-primary);
    font-size: 18px;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 8px;
}

.template-form .form-group {
    margin-bottom: 12px;
}

.template-form label {
    display: block;
    margin-bottom: 4px;
    font-weight: 500;
    color: var(--text-primary);
    font-size: 14px;
}

.template-form input[type="checkbox"] {
    margin-right: 8px;
}

.template-item {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
    transition: all 0.3s ease;
}

.template-item:hover {
    border-color: var(--accent-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.template-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.template-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 16px;
}

.template-actions {
    display: flex;
    gap: 4px;
}

.apply-template-btn,
.delete-template-btn {
    background: none;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 4px 8px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.apply-template-btn {
    color: var(--success-color);
    border-color: var(--success-color);
}

.apply-template-btn:hover {
    background: var(--success-color);
    color: white;
}

.delete-template-btn {
    color: var(--error-color);
    border-color: var(--error-color);
}

.delete-template-btn:hover {
    background: var(--error-color);
    color: white;
}

.template-settings {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-bottom: 8px;
}

.setting-tag {
    background: var(--accent-color);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.template-description {
    color: var(--text-secondary);
    font-size: 14px;
    margin-bottom: 8px;
    font-style: italic;
}

.template-meta {
    color: var(--text-secondary);
    font-size: 12px;
}

.no-templates {
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
    padding: 32px;
    background: var(--bg-secondary);
    border-radius: 8px;
    border: 1px dashed var(--border-color);
}

/* Responsive adjustments for templates */
@media (max-width: 768px) {
    .templates-section {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .template-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .template-item-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .template-actions {
        align-self: flex-end;
    }
}

/* Smart Notifications Styles */
.notifications-button {
    background: var(--accent-color);
    border: none;
    border-radius: 8px;
    padding: 12px;
    color: white;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;
    margin-left: 8px;
    min-width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notifications-button:hover {
    background: var(--accent-hover);
    transform: translateY(-2px);
}

.notification-settings {
    max-width: 500px;
}

.setting-group {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--border-color);
}

.setting-group:last-child {
    border-bottom: none;
}

.setting-group h3 {
    margin: 0 0 12px 0;
    color: var(--text-primary);
    font-size: 16px;
}

.setting-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.setting-item:hover {
    background: var(--bg-secondary);
}

.setting-item input[type="checkbox"] {
    margin-right: 12px;
    width: 16px;
    height: 16px;
    accent-color: var(--accent-color);
}

.setting-item span {
    color: var(--text-primary);
    font-size: 14px;
}

.setting-group .btn-secondary {
    margin-right: 8px;
    margin-bottom: 8px;
    padding: 6px 12px;
    font-size: 12px;
}

/* Enhanced notification toast styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 10000;
    max-width: 350px;
    animation: slideInRight 0.3s ease;
}

.notification.success {
    border-left: 4px solid var(--success-color);
}

.notification.error {
    border-left: 4px solid var(--error-color);
}

.notification.warning {
    border-left: 4px solid var(--warning-color);
}

.notification.info {
    border-left: 4px solid var(--accent-color);
}

.notification.progress {
    border-left: 4px solid var(--accent-color);
}

.notification-content {
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.notification-icon {
    font-size: 20px;
    flex-shrink: 0;
}

.notification-text {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
    font-size: 14px;
}

.notification-message {
    color: var(--text-secondary);
    font-size: 13px;
    line-height: 1.4;
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 18px;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    flex-shrink: 0;
}

.notification-close:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.notification.hiding {
    animation: slideOutRight 0.3s ease forwards;
}

/* Responsive adjustments for notifications */
@media (max-width: 768px) {
    .notifications-button {
        padding: 10px;
        min-width: 44px;
        height: 44px;
        font-size: 14px;
    }

    .notification {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }

    .setting-group .btn-secondary {
        width: 100%;
        margin-right: 0;
    }
}

/* Advanced Queue Management Styles */
.queue-management-button {
    background: var(--accent-color);
    border: none;
    border-radius: 8px;
    padding: 12px;
    color: white;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;
    margin-left: 8px;
    min-width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.queue-management-button:hover {
    background: var(--accent-hover);
    transform: translateY(-2px);
}

.queue-management-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 20px;
}

.tab-button {
    background: none;
    border: none;
    padding: 12px 20px;
    cursor: pointer;
    color: var(--text-secondary);
    font-size: 14px;
    font-weight: 500;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.tab-button:hover {
    color: var(--text-primary);
    background: var(--bg-secondary);
}

.tab-button.active {
    color: var(--accent-color);
    border-bottom-color: var(--accent-color);
}

.tab-panel {
    display: none;
}

.tab-panel.active {
    display: block;
}

.settings-section {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--border-color);
}

.settings-section:last-child {
    border-bottom: none;
}

.settings-section h3 {
    margin: 0 0 16px 0;
    color: var(--text-primary);
    font-size: 16px;
}

.settings-section small {
    color: var(--text-secondary);
    font-size: 12px;
    display: block;
    margin-top: 4px;
}

.priority-legend {
    display: flex;
    gap: 16px;
    margin-top: 8px;
    font-size: 12px;
}

.priority-legend span {
    display: flex;
    align-items: center;
    gap: 4px;
}

.queue-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 8px;
}

.queue-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    margin-bottom: 8px;
    background: var(--bg-secondary);
    border-radius: 6px;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.queue-item:hover {
    border-color: var(--accent-color);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.queue-item-info {
    flex: 1;
}

.queue-item-title {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 300px;
}

.queue-item-details {
    font-size: 12px;
    color: var(--text-secondary);
}

.queue-item-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.priority-select {
    padding: 4px 8px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 12px;
}

.move-up-btn,
.move-down-btn {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 4px 8px;
    cursor: pointer;
    color: var(--text-primary);
    font-size: 12px;
    transition: all 0.3s ease;
}

.move-up-btn:hover,
.move-down-btn:hover {
    background: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

.optimization-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
    padding: 16px;
    background: var(--bg-secondary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.stat-item {
    text-align: center;
}

.stat-label {
    display: block;
    font-size: 12px;
    color: var(--text-secondary);
    margin-bottom: 4px;
}

.stat-value {
    display: block;
    font-size: 18px;
    font-weight: 600;
    color: var(--accent-color);
}

.optimization-actions {
    display: flex;
    gap: 12px;
    margin-bottom: 24px;
}

.optimization-actions .btn-primary,
.optimization-actions .btn-secondary {
    flex: 1;
    padding: 12px;
    font-size: 14px;
}

.optimization-suggestions h4 {
    margin: 0 0 12px 0;
    color: var(--text-primary);
    font-size: 14px;
}

.suggestion-item {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 8px;
    font-size: 13px;
    color: var(--text-primary);
}

.no-queue-items {
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
    padding: 32px;
    background: var(--bg-secondary);
    border-radius: 8px;
    border: 1px dashed var(--border-color);
}

/* Responsive adjustments for queue management */
@media (max-width: 768px) {
    .queue-management-button {
        padding: 10px;
        min-width: 44px;
        height: 44px;
        font-size: 14px;
    }

    .queue-management-tabs {
        flex-direction: column;
    }

    .tab-button {
        text-align: left;
        border-bottom: none;
        border-right: 2px solid transparent;
    }

    .tab-button.active {
        border-bottom: none;
        border-right-color: var(--accent-color);
    }

    .optimization-stats {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .optimization-actions {
        flex-direction: column;
    }

    .queue-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .queue-item-controls {
        align-self: flex-end;
    }

    .queue-item-title {
        max-width: none;
        white-space: normal;
        overflow: visible;
        text-overflow: unset;
    }
}

/* User Onboarding Experience Styles */
.onboarding-modal .modal-content {
    max-width: 600px;
    padding: 0;
    border-radius: 16px;
    overflow: hidden;
}

.welcome-header {
    background: linear-gradient(135deg, var(--accent-color), var(--accent-hover));
    color: white;
    text-align: center;
    padding: 40px 32px;
}

.welcome-icon {
    font-size: 48px;
    margin-bottom: 16px;
}

.welcome-header h1 {
    margin: 0 0 8px 0;
    font-size: 28px;
    font-weight: 600;
}

.welcome-header p {
    margin: 0;
    font-size: 16px;
    opacity: 0.9;
}

.welcome-body {
    padding: 32px;
}

.welcome-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
}

.feature-item {
    text-align: center;
}

.feature-icon {
    font-size: 32px;
    margin-bottom: 12px;
}

.feature-item h3 {
    margin: 0 0 8px 0;
    font-size: 16px;
    color: var(--text-primary);
}

.feature-item p {
    margin: 0;
    font-size: 13px;
    color: var(--text-secondary);
    line-height: 1.4;
}

.welcome-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
}

.welcome-actions button {
    padding: 12px 24px;
    font-size: 14px;
    min-width: 120px;
}

/* Tour Overlay Styles */
.tour-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    pointer-events: none;
}

.tour-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(2px);
}

.tour-spotlight {
    position: absolute;
    background: transparent;
    border: 3px solid var(--accent-color);
    border-radius: 8px;
    box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
    z-index: 10001;
}

.tour-tooltip {
    position: absolute;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    max-width: 320px;
    z-index: 10002;
    pointer-events: auto;
    animation: tourTooltipIn 0.3s ease;
}

@keyframes tourTooltipIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(10px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.tour-content {
    padding: 20px;
}

.tour-title {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

.tour-description {
    margin: 0 0 16px 0;
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.5;
}

.tour-progress {
    display: flex;
    align-items: center;
    gap: 12px;
}

.tour-step-counter {
    font-size: 12px;
    color: var(--text-secondary);
    white-space: nowrap;
}

.tour-progress-bar {
    flex: 1;
    height: 4px;
    background: var(--bg-secondary);
    border-radius: 2px;
    overflow: hidden;
}

.tour-progress-fill {
    height: 100%;
    background: var(--accent-color);
    border-radius: 2px;
    transition: width 0.3s ease;
}

.tour-controls {
    padding: 16px 20px;
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.tour-controls button {
    padding: 8px 16px;
    font-size: 13px;
}

/* Help Button Styles */
.help-button {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: var(--accent-color);
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    z-index: 1000;
}

.help-button:hover {
    background: var(--accent-hover);
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.help-menu {
    position: fixed;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    z-index: 1001;
    animation: helpMenuIn 0.2s ease;
}

@keyframes helpMenuIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(10px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.help-menu-content {
    padding: 16px;
}

.help-menu-content h3 {
    margin: 0 0 12px 0;
    font-size: 16px;
    color: var(--text-primary);
}

.help-menu-items {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.help-menu-items button {
    background: none;
    border: none;
    padding: 8px 12px;
    text-align: left;
    color: var(--text-primary);
    cursor: pointer;
    border-radius: 6px;
    font-size: 13px;
    transition: background 0.2s ease;
}

.help-menu-items button:hover {
    background: var(--bg-secondary);
}

/* Tip System Styles */
.tip-container {
    position: fixed;
    bottom: 80px;
    right: 20px;
    max-width: 300px;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    animation: tipIn 0.3s ease;
}

@keyframes tipIn {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.tip-content {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 16px;
}

.tip-icon {
    font-size: 20px;
    flex-shrink: 0;
}

.tip-text {
    flex: 1;
    font-size: 13px;
    color: var(--text-primary);
    line-height: 1.4;
}

.tip-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 16px;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.tip-close:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

/* Responsive adjustments for onboarding */
@media (max-width: 768px) {
    .welcome-features {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .welcome-actions {
        flex-direction: column;
    }

    .tour-tooltip {
        max-width: 280px;
        margin: 0 20px;
    }

    .tour-controls {
        flex-wrap: wrap;
        gap: 8px;
    }

    .help-button {
        bottom: 16px;
        right: 16px;
        width: 44px;
        height: 44px;
        font-size: 16px;
    }

    .tip-container {
        bottom: 70px;
        right: 16px;
        left: 16px;
        max-width: none;
    }

    .help-menu {
        right: 16px !important;
        left: 16px;
        max-width: none;
    }
}

/* Error Solution Modal Styles */
.error-solution-modal .modal-content {
    max-width: 600px;
}

.error-details {
    background: var(--bg-secondary);
    border-radius: 8px;
    padding: 20px;
    border: 1px solid var(--border-color);
}

.error-details h3 {
    margin: 0 0 8px 0;
    color: var(--text-primary);
    font-size: 16px;
}

.error-message {
    background: #fee;
    border: 1px solid #fcc;
    border-radius: 4px;
    padding: 8px 12px;
    margin: 0 0 16px 0;
    color: #c33;
    font-family: monospace;
    font-size: 13px;
}

.solution-text {
    background: #efe;
    border: 1px solid #cfc;
    border-radius: 4px;
    padding: 8px 12px;
    margin: 0 0 16px 0;
    color: #363;
    font-weight: 500;
}

.detailed-solution {
    margin-top: 16px;
}

.detailed-solution h4 {
    margin: 0 0 12px 0;
    color: var(--text-primary);
    font-size: 14px;
}

.detailed-solution ol {
    margin: 0 0 16px 0;
    padding-left: 20px;
}

.detailed-solution li {
    margin-bottom: 8px;
    line-height: 1.4;
}

.detailed-solution ul {
    margin: 4px 0;
    padding-left: 20px;
}

.detailed-solution code {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 3px;
    padding: 2px 6px;
    font-family: monospace;
    font-size: 12px;
    color: var(--accent-color);
}

.detailed-solution a {
    color: var(--accent-color);
    text-decoration: none;
}

.detailed-solution a:hover {
    text-decoration: underline;
}

.detailed-solution .tip {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 12px;
    margin-top: 12px;
    color: #856404;
    font-size: 13px;
}

/* Fix Button Styles */
.fix-button {
    background: #ff9500 !important;
    color: white !important;
}

.fix-button:hover {
    background: #e6850e !important;
    transform: scale(1.05);
}

/* Enhanced Error Display in Downloads */
.download-status .status-text {
    max-width: 400px;
    word-wrap: break-word;
    line-height: 1.3;
}

.status-error .status-text {
    color: #dc3545;
    font-size: 12px;
}

/* Responsive adjustments for error modal */
@media (max-width: 768px) {
    .error-solution-modal .modal-content {
        max-width: 90vw;
        margin: 20px;
    }

    .detailed-solution ol,
    .detailed-solution ul {
        padding-left: 16px;
    }

    .error-details {
        padding: 16px;
    }

    .modal-footer {
        flex-direction: column;
        gap: 8px;
    }

    .modal-footer button {
        width: 100%;
    }
}
