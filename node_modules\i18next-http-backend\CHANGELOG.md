### 3.0.2

- optimize fetchApi selector

### 3.0.1

- try to get rid of top-level await

### 3.0.0

- fix for Deno 2 and removal of unnecessary .cjs file
- for esm build environments not supporting top-level await, you should import the `i18next-http-backend/cjs` export or stay at v2.6.2 or v2.7.1

### 2.7.1
- same as 2.6.2

### 2.7.0
- deprecated, same as v3.0.0

### 2.6.2

- improve network error detection across browsers [152](https://github.com/i18next/i18next-http-backend/pull/152)

### 2.6.1

- optimize "Failed to fetch" retry case [147](https://github.com/i18next/i18next-http-backend/issues/147)

### 2.6.0

- fix "Failed to fetch" retry case [147](https://github.com/i18next/i18next-http-backend/issues/147)

### 2.5.2

- dereference timers in node.js so that the process may exit when finished [139](https://github.com/i18next/i18next-http-backend/pull/139)

### 2.5.1

- fix: remove typeof window.document === 'undefined' check which deopt bundle optimization [137](https://github.com/i18next/i18next-http-backend/pull/137)

### 2.5.0

- added fetch interceptor to the Backend Options [133](https://github.com/i18next/i18next-http-backend/pull/133)

### 2.4.3

- fix: overriding options

### 2.4.2

- fix: mjs typings export

### 2.4.1

- fix: separate cjs and mjs typings

### 2.3.1

- fix for browser usage

### 2.3.0

- update deps

### 2.2.2

- hack for debug mode in react-native

### 2.2.1

- fix for types moduleResolution "node16"

### 2.2.0

- parseLoadPayload for POST request [110](https://github.com/i18next/i18next-http-backend/pull/110)

### 2.1.1

- regression fix for saveMissing signature [1890](https://github.com/i18next/i18next/issues/1890)

### 2.1.0

- typescript: export the backend options type [105](https://github.com/i18next/i18next-http-backend/pull/105)

### 2.0.2

- typescript: static type prop

### 2.0.1

- fix if url starts with file:// [100](https://github.com/i18next/i18next-http-backend/issues/100)

### 2.0.0

- typescript: update for major i18next version

