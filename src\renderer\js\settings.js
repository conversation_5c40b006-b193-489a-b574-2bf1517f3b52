// Settings management functionality
class SettingsManager {
    constructor() {
        this.setupSettingsModal();
    }

    setupSettingsModal() {
        // Load settings content when modal is opened
        document.getElementById('settings-button').addEventListener('click', () => {
            this.loadSettingsContent();
        });

        // Also handle menu-triggered settings
        window.electronAPI.onMenuSettings(() => {
            this.loadSettingsContent();
            document.getElementById('settings-modal').classList.remove('hidden');
        });
    }

    loadSettingsContent() {
        const modalBody = document.querySelector('#settings-modal .modal-body');
        
        modalBody.innerHTML = `
            <div class="settings-content">
                <div class="settings-section">
                    <h3 data-i18n="settings.general">General</h3>
                    <div class="setting-item">
                        <label for="language-setting" data-i18n="settings.language">Language</label>
                        <select id="language-setting" class="select-input">
                            <!-- Language options will be populated dynamically -->
                        </select>
                    </div>
                    <div class="setting-item">
                        <label for="theme-setting" data-i18n="settings.theme">Theme</label>
                        <select id="theme-setting" class="select-input">
                            <option value="light">Light</option>
                            <option value="dark">Dark</option>
                            <option value="system">System</option>
                        </select>
                    </div>
                </div>

                <div class="settings-section">
                    <h3 data-i18n="settings.downloads">Downloads</h3>
                    <div class="setting-item">
                        <label class="toggle-switch">
                            <input type="checkbox" id="auto-clipboard-setting">
                            <span class="slider"></span>
                            <span class="toggle-label" data-i18n="settings.autoClipboard">Auto-detect clipboard URLs</span>
                        </label>
                        <p class="setting-description">Automatically detect video URLs when copied to clipboard</p>
                    </div>
                    
                    <div class="setting-item">
                        <label for="default-quality-setting">Default Quality</label>
                        <select id="default-quality-setting" class="select-input">
                            <option value="best">Best Available</option>
                            <option value="8k">UHD 8K</option>
                            <option value="4k">HD 4K</option>
                            <option value="2k">HQ 2K</option>
                            <option value="1080p">1080p</option>
                            <option value="720p">720p</option>
                            <option value="480p">480p</option>
                            <option value="360p">360p</option>
                            <option value="240p">240p</option>
                            <option value="worst">Lowest Quality</option>
                        </select>
                    </div>
                    
                    <div class="setting-item">
                        <label for="default-format-setting">Default Format</label>
                        <select id="default-format-setting" class="select-input">
                            <option value="auto">Auto</option>
                            <option value="mp4">MP4</option>
                            <option value="mkv">MKV</option>
                            <option value="webm">WebM</option>
                            <option value="mp3">MP3 (Audio)</option>
                            <option value="aac">AAC (Audio)</option>
                            <option value="ogg">OGG (Audio)</option>
                        </select>
                    </div>
                    
                    <div class="setting-item">
                        <label for="default-destination-setting">Default Download Location</label>
                        <div class="destination-wrapper">
                            <select id="default-destination-setting" class="select-input">
                                <option value="downloads">Downloads Folder</option>
                                <option value="videos">Videos Folder</option>
                                <option value="music">Music Folder</option>
                                <option value="documents">Documents Folder</option>
                                <option value="custom">Custom Location</option>
                            </select>
                            <button id="browse-destination" class="browse-button" title="Browse for folder">
                                📁
                            </button>
                        </div>
                        <div id="custom-destination-path" class="custom-path hidden">
                            <input type="text" id="custom-path-input" class="url-input" readonly placeholder="No custom path selected">
                        </div>
                    </div>
                    
                    <div class="setting-item">
                        <label for="max-concurrent-setting">Maximum Concurrent Downloads</label>
                        <select id="max-concurrent-setting" class="select-input">
                            <option value="1">1</option>
                            <option value="2">2</option>
                            <option value="3">3</option>
                            <option value="4">4</option>
                            <option value="5">5</option>
                        </select>
                    </div>
                </div>

                <div class="settings-section">
                    <h3>Advanced</h3>
                    <div class="setting-item">
                        <label class="toggle-switch">
                            <input type="checkbox" id="auto-update-setting">
                            <span class="slider"></span>
                            <span class="toggle-label">Auto-update yt-dlp</span>
                        </label>
                        <p class="setting-description">Automatically update the download engine for better compatibility</p>
                    </div>
                    
                    <div class="setting-item">
                        <label class="toggle-switch">
                            <input type="checkbox" id="extract-audio-setting">
                            <span class="slider"></span>
                            <span class="toggle-label">Extract audio tracks</span>
                        </label>
                        <p class="setting-description">Extract separate audio tracks when available</p>
                    </div>
                    
                    <div class="setting-item">
                        <label class="toggle-switch">
                            <input type="checkbox" id="download-subtitles-setting">
                            <span class="slider"></span>
                            <span class="toggle-label">Download subtitles</span>
                        </label>
                        <p class="setting-description">Automatically download subtitle files when available</p>
                    </div>
                    
                    <div class="setting-item">
                        <label for="subtitle-language-setting">Subtitle Language</label>
                        <select id="subtitle-language-setting" class="select-input">
                            <option value="en">English</option>
                            <option value="es">Spanish</option>
                            <option value="fr">French</option>
                            <option value="de">German</option>
                            <option value="it">Italian</option>
                            <option value="pt">Portuguese</option>
                            <option value="ru">Russian</option>
                            <option value="ja">Japanese</option>
                            <option value="ko">Korean</option>
                            <option value="zh">Chinese</option>
                            <option value="all">All Available</option>
                        </select>
                    </div>
                </div>

                <div class="settings-section">
                    <h3>Privacy & Security</h3>
                    <div class="setting-item">
                        <label class="toggle-switch">
                            <input type="checkbox" id="clear-history-setting">
                            <span class="slider"></span>
                            <span class="toggle-label">Clear download history on exit</span>
                        </label>
                        <p class="setting-description">Automatically clear the download list when closing the app</p>
                    </div>
                    
                    <div class="setting-item">
                        <label class="toggle-switch">
                            <input type="checkbox" id="use-cookies-setting">
                            <span class="slider"></span>
                            <span class="toggle-label">Use browser cookies</span>
                        </label>
                        <p class="setting-description">Use browser cookies for accessing private content (use responsibly)</p>
                    </div>
                </div>

                <div class="settings-actions">
                    <button id="reset-settings" class="secondary-button">Reset to Defaults</button>
                    <div class="action-buttons">
                        <button id="cancel-settings" class="secondary-button">Cancel</button>
                        <button id="save-settings" class="download-button">Save Settings</button>
                    </div>
                </div>
            </div>
        `;

        this.populateLanguageSelector();
        this.loadCurrentSettings();
        this.setupSettingsEventListeners();
    }

    populateLanguageSelector() {
        const languageSelect = document.getElementById('language-setting');
        if (!languageSelect || !window.getSupportedLanguages) return;

        const languages = window.getSupportedLanguages();
        languageSelect.innerHTML = '';

        languages.forEach(lang => {
            const option = document.createElement('option');
            option.value = lang.code;
            option.textContent = lang.name;
            languageSelect.appendChild(option);
        });
    }

    async loadCurrentSettings() {
        try {
            const settings = await window.electronAPI.getSettings() || {};

            // Apply current settings to form
            if (document.getElementById('language-setting')) {
                document.getElementById('language-setting').value = settings.language || window.getCurrentLanguage() || 'en';
            }
            document.getElementById('theme-setting').value = settings.theme || 'light';
            document.getElementById('auto-clipboard-setting').checked = settings.autoClipboard || false;
            document.getElementById('default-quality-setting').value = settings.defaultQuality || 'best';
            document.getElementById('default-format-setting').value = settings.defaultFormat || 'auto';
            document.getElementById('default-destination-setting').value = settings.defaultDestination || 'downloads';
            document.getElementById('max-concurrent-setting').value = settings.maxConcurrent || '3';
            document.getElementById('auto-update-setting').checked = settings.autoUpdate !== false;
            document.getElementById('extract-audio-setting').checked = settings.extractAudio || false;
            document.getElementById('download-subtitles-setting').checked = settings.downloadSubtitles || false;
            document.getElementById('subtitle-language-setting').value = settings.subtitleLanguage || 'en';
            document.getElementById('clear-history-setting').checked = settings.clearHistory || false;
            document.getElementById('use-cookies-setting').checked = settings.useCookies || false;
            
            // Handle custom destination
            if (settings.defaultDestination === 'custom' && settings.customDestination) {
                document.getElementById('custom-destination-path').classList.remove('hidden');
                document.getElementById('custom-path-input').value = settings.customDestination;
            }
            
        } catch (error) {
            console.error('Failed to load settings:', error);
        }
    }

    setupSettingsEventListeners() {
        // Language change handler
        const languageSelect = document.getElementById('language-setting');
        if (languageSelect) {
            languageSelect.addEventListener('change', async (e) => {
                const newLanguage = e.target.value;
                if (window.changeLanguage) {
                    await window.changeLanguage(newLanguage);
                    // Update all translated elements immediately
                    if (window.i18nManager) {
                        window.i18nManager.updateTranslatedElements();
                    }
                }
            });
        }

        // Destination change handler
        document.getElementById('default-destination-setting').addEventListener('change', (e) => {
            const customPathDiv = document.getElementById('custom-destination-path');
            if (e.target.value === 'custom') {
                customPathDiv.classList.remove('hidden');
            } else {
                customPathDiv.classList.add('hidden');
            }
        });

        // Browse for custom destination
        document.getElementById('browse-destination').addEventListener('click', async () => {
            try {
                const result = await window.electronAPI.showOpenDialog({
                    properties: ['openDirectory'],
                    title: 'Select Download Folder'
                });
                
                if (!result.canceled && result.filePaths.length > 0) {
                    document.getElementById('custom-path-input').value = result.filePaths[0];
                    document.getElementById('default-destination-setting').value = 'custom';
                    document.getElementById('custom-destination-path').classList.remove('hidden');
                }
            } catch (error) {
                console.error('Failed to browse for folder:', error);
                window.app.showNotification('Failed to browse for folder', 'error');
            }
        });

        // Reset settings
        document.getElementById('reset-settings').addEventListener('click', () => {
            if (confirm('Are you sure you want to reset all settings to defaults?')) {
                this.resetToDefaults();
            }
        });

        // Cancel settings
        document.getElementById('cancel-settings').addEventListener('click', () => {
            document.getElementById('settings-modal').classList.add('hidden');
        });

        // Save settings
        document.getElementById('save-settings').addEventListener('click', () => {
            this.saveSettings();
        });
    }

    async saveSettings() {
        try {
            const settings = {
                language: document.getElementById('language-setting') ? document.getElementById('language-setting').value : 'en',
                theme: document.getElementById('theme-setting').value,
                autoClipboard: document.getElementById('auto-clipboard-setting').checked,
                defaultQuality: document.getElementById('default-quality-setting').value,
                defaultFormat: document.getElementById('default-format-setting').value,
                defaultDestination: document.getElementById('default-destination-setting').value,
                customDestination: document.getElementById('custom-path-input').value,
                maxConcurrent: parseInt(document.getElementById('max-concurrent-setting').value),
                autoUpdate: document.getElementById('auto-update-setting').checked,
                extractAudio: document.getElementById('extract-audio-setting').checked,
                downloadSubtitles: document.getElementById('download-subtitles-setting').checked,
                subtitleLanguage: document.getElementById('subtitle-language-setting').value,
                clearHistory: document.getElementById('clear-history-setting').checked,
                useCookies: document.getElementById('use-cookies-setting').checked
            };

            await window.electronAPI.saveSettings(settings);
            
            // Update app settings
            if (window.app) {
                window.app.settings = settings;
                
                // Apply theme change if needed
                if (settings.theme !== document.body.className.replace('theme-', '')) {
                    window.app.settings.theme = settings.theme;
                    document.body.className = `theme-${settings.theme}`;
                    
                    // Update theme toggle icon
                    const themeToggle = document.getElementById('theme-toggle');
                    themeToggle.querySelector('.icon').textContent = settings.theme === 'light' ? '🌙' : '☀️';
                }
                
                // Update clipboard monitoring
                const autoClipboardToggle = document.getElementById('auto-paste-toggle');
                if (autoClipboardToggle.checked !== settings.autoClipboard) {
                    autoClipboardToggle.checked = settings.autoClipboard;
                    if (settings.autoClipboard) {
                        window.app.startClipboardMonitoring();
                    } else {
                        window.app.stopClipboardMonitoring();
                    }
                }
            }

            window.app.showNotification('Settings saved successfully', 'success');
            document.getElementById('settings-modal').classList.add('hidden');
            
        } catch (error) {
            console.error('Failed to save settings:', error);
            window.app.showNotification('Failed to save settings', 'error');
        }
    }

    resetToDefaults() {
        // Reset all form fields to default values
        if (document.getElementById('language-setting')) {
            document.getElementById('language-setting').value = 'en';
        }
        document.getElementById('theme-setting').value = 'light';
        document.getElementById('auto-clipboard-setting').checked = false;
        document.getElementById('default-quality-setting').value = 'best';
        document.getElementById('default-format-setting').value = 'auto';
        document.getElementById('default-destination-setting').value = 'downloads';
        document.getElementById('max-concurrent-setting').value = '3';
        document.getElementById('auto-update-setting').checked = true;
        document.getElementById('extract-audio-setting').checked = false;
        document.getElementById('download-subtitles-setting').checked = false;
        document.getElementById('subtitle-language-setting').value = 'en';
        document.getElementById('clear-history-setting').checked = false;
        document.getElementById('use-cookies-setting').checked = false;
        
        // Hide custom destination
        document.getElementById('custom-destination-path').classList.add('hidden');
        document.getElementById('custom-path-input').value = '';
        
        window.app.showNotification('Settings reset to defaults', 'info');
    }
}

// Add CSS for settings
const settingsCSS = `
.settings-content {
    max-height: 60vh;
    overflow-y: auto;
}

.settings-section {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.settings-section:last-of-type {
    border-bottom: none;
}

.settings-section h3 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.setting-item {
    margin-bottom: 1.5rem;
}

.setting-item:last-child {
    margin-bottom: 0;
}

.setting-item label:not(.toggle-switch) {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
}

.setting-description {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-top: 0.25rem;
    line-height: 1.4;
}

.custom-path {
    margin-top: 0.5rem;
}

.settings-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-color);
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
}
`;

// Inject settings CSS
const style = document.createElement('style');
style.textContent = settingsCSS;
document.head.appendChild(style);

// Initialize settings manager
const settingsManager = new SettingsManager();
