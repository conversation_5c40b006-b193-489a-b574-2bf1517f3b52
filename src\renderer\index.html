<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https:;">
    <title>Downloader Pro</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/themes.css">
</head>
<body class="theme-light">
    <div id="app">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <div class="logo">
                    <img src="../../assets/icon.png" alt="Downloader Pro" class="logo-icon">
                    <h1 class="logo-text" data-i18n="app.title">Downloader Pro</h1>
                </div>
                <div class="header-controls">
                    <button id="theme-toggle" class="icon-button" data-i18n-title="header.toggleTheme" title="Toggle Theme">
                        <span class="icon">🌙</span>
                    </button>
                    <button id="settings-button" class="icon-button" data-i18n-title="header.settings" title="Settings">
                        <span class="icon">⚙️</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- URL Input Section -->
            <section class="url-input-section">
                <div class="input-container">
                    <div class="url-input-wrapper">
                        <input type="text" id="url-input" data-i18n-placeholder="input.urlPlaceholder" placeholder="Paste video URL here..." class="url-input">
                        <button id="paste-button" class="paste-button" data-i18n-title="input.pasteFromClipboard" title="Paste from clipboard">
                            <span class="icon">📋</span>
                        </button>
                    </div>
                    <div class="input-controls">
                        <label class="toggle-switch">
                            <input type="checkbox" id="auto-paste-toggle">
                            <span class="slider"></span>
                            <span class="toggle-label" data-i18n="input.autoDetectClipboard">Auto-detect clipboard</span>
                        </label>
                        <button id="download-button" class="download-button" disabled>
                            <span class="icon">⬇️</span>
                            <span class="text" data-i18n="input.download">Download</span>
                        </button>
                        <button id="schedule-button" class="schedule-button" onclick="window.app.showSchedulerModal()" data-i18n-title="input.scheduleDownload" title="Schedule Download">
                            <span class="icon">⏰</span>
                        </button>
                        <button id="notifications-button" class="notifications-button" onclick="window.app.showNotificationSettingsModal()" data-i18n-title="input.notificationSettings" title="Notification Settings">
                            <span class="icon">🔔</span>
                        </button>
                        <button id="queue-management-button" class="queue-management-button" onclick="window.app.showQueueManagementModal()" data-i18n-title="input.queueManagement" title="Queue Management">
                            <span class="icon">⚡</span>
                        </button>
                    </div>
                </div>
            </section>

            <!-- Platform Icons Section -->
            <section class="platform-section">
                <div class="platform-header">
                    <h2 data-i18n="platform.header">Find videos to download right in the app</h2>
                    <p data-i18n="platform.description">Tap the service icon below and start searching</p>
                </div>
                <div class="platform-icons">
                    <button class="platform-icon" data-platform="youtube" data-i18n-title="platform.youtube" title="YouTube">
                        <img src="../../assets/platforms/youtube.png" alt="YouTube">
                        <span data-i18n="platform.youtube">YouTube</span>
                    </button>
                    <button class="platform-icon" data-platform="facebook" data-i18n-title="platform.facebook" title="Facebook">
                        <img src="../../assets/platforms/facebook.png" alt="Facebook">
                        <span data-i18n="platform.facebook">Facebook</span>
                    </button>
                    <button class="platform-icon" data-platform="instagram" data-i18n-title="platform.instagram" title="Instagram">
                        <img src="../../assets/platforms/instagram.png" alt="Instagram">
                        <span data-i18n="platform.instagram">Instagram</span>
                    </button>
                    <button class="platform-icon" data-platform="tiktok" data-i18n-title="platform.tiktok" title="TikTok">
                        <img src="../../assets/platforms/tiktok.png" alt="TikTok">
                        <span data-i18n="platform.tiktok">TikTok</span>
                    </button>
                    <button class="platform-icon" data-platform="twitter" data-i18n-title="platform.twitter" title="Twitter">
                        <img src="../../assets/platforms/twitter.png" alt="Twitter">
                        <span data-i18n="platform.twitter">Twitter</span>
                    </button>
                </div>
            </section>

            <!-- Video Preview Section -->
            <section id="video-preview" class="video-preview hidden">
                <div class="preview-card">
                    <div class="preview-thumbnail">
                        <img id="preview-image" src="" alt="Video thumbnail">
                        <div class="preview-duration" id="preview-duration"></div>
                    </div>
                    <div class="preview-info">
                        <h3 id="preview-title"></h3>
                        <p id="preview-description"></p>
                        <div class="preview-meta">
                            <span id="preview-uploader"></span>
                            <span id="preview-views"></span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Download Options Section -->
            <section id="download-options" class="download-options hidden">
                <div class="options-grid">
                    <div class="option-group">
                        <label for="download-type" data-i18n="downloadOptions.downloadType">Download Type</label>
                        <select id="download-type" class="select-input">
                            <option value="video" data-i18n="downloadOptions.types.video">Video</option>
                            <option value="audio" data-i18n="downloadOptions.types.audio">Audio Only</option>
                            <option value="subtitles" data-i18n="downloadOptions.types.subtitles">Subtitles Only</option>
                            <option value="audio-tracks" data-i18n="downloadOptions.types.audioTracks">Audio Tracks</option>
                        </select>
                    </div>

                    <div class="option-group">
                        <label for="quality-select" data-i18n="downloadOptions.quality">Quality/Resolution</label>
                        <select id="quality-select" class="select-input">
                            <option value="best" data-i18n="downloadOptions.qualities.best">Best Available</option>
                            <option value="8k" data-i18n="downloadOptions.qualities.8k">UHD 8K</option>
                            <option value="4k" data-i18n="downloadOptions.qualities.4k">HD 4K</option>
                            <option value="2k" data-i18n="downloadOptions.qualities.2k">HQ 2K</option>
                            <option value="1080p" data-i18n="downloadOptions.qualities.1080p">1080p</option>
                            <option value="720p" data-i18n="downloadOptions.qualities.720p">720p</option>
                            <option value="480p" data-i18n="downloadOptions.qualities.480p">480p</option>
                            <option value="360p" data-i18n="downloadOptions.qualities.360p">360p</option>
                            <option value="240p" data-i18n="downloadOptions.qualities.240p">240p</option>
                            <option value="worst" data-i18n="downloadOptions.qualities.worst">Lowest Quality</option>
                        </select>
                    </div>
                    
                    <div class="option-group">
                        <label for="format-select" data-i18n="downloadOptions.format">Output Format</label>
                        <select id="format-select" class="select-input">
                            <option value="auto" data-i18n="downloadOptions.formats.auto">Auto</option>
                            <option value="mp4" data-i18n="downloadOptions.formats.mp4">MP4</option>
                            <option value="mkv" data-i18n="downloadOptions.formats.mkv">MKV</option>
                            <option value="webm" data-i18n="downloadOptions.formats.webm">WebM</option>
                            <option value="mp3" data-i18n="downloadOptions.formats.mp3">MP3 (Audio)</option>
                            <option value="aac" data-i18n="downloadOptions.formats.aac">AAC (Audio)</option>
                            <option value="ogg" data-i18n="downloadOptions.formats.ogg">OGG (Audio)</option>
                        </select>
                    </div>

                    <div class="option-group">
                        <label for="destination-select" data-i18n="downloadOptions.destination">Destination</label>
                        <div class="destination-wrapper">
                            <select id="destination-select" class="select-input">
                                <option value="downloads" data-i18n="downloadOptions.destinations.downloads">Downloads</option>
                                <option value="videos" data-i18n="downloadOptions.destinations.videos">Videos</option>
                                <option value="music" data-i18n="downloadOptions.destinations.music">Music</option>
                                <option value="documents" data-i18n="downloadOptions.destinations.documents">Documents</option>
                                <option value="custom" data-i18n="downloadOptions.destinations.custom">Browse...</option>
                            </select>
                            <button id="browse-button" class="browse-button" data-i18n-title="downloadOptions.browse" title="Browse for folder">
                                <span class="icon">📁</span>
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Downloads Section -->
            <section class="downloads-section">
                <div class="section-header">
                    <h2 data-i18n="downloads.title">Downloads</h2>
                    <div class="download-controls">
                        <button id="clear-completed" class="secondary-button" data-i18n="downloads.clearCompleted">Clear Completed</button>
                        <button id="pause-all" class="secondary-button" data-i18n="downloads.pauseAll">Pause All</button>
                        <button id="show-history" class="secondary-button">
                            <span class="icon">📜</span>
                            <span data-i18n="downloads.history">History</span>
                        </button>
                    </div>
                </div>
                <div id="downloads-list" class="downloads-list">
                    <div class="empty-state">
                        <div class="empty-icon">📥</div>
                        <h3 data-i18n="downloads.empty.title">No downloads yet</h3>
                        <p data-i18n="downloads.empty.description">Paste a video URL above to get started</p>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Modals -->
    <div id="settings-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h2 data-i18n="settings.title">Settings</h2>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <!-- Settings content will be loaded here -->
            </div>
        </div>
    </div>

    <div id="about-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h2 data-i18n="about.title">About Downloader Pro</h2>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="about-content">
                    <img src="../../assets/icon.png" alt="Downloader Pro" class="about-icon">
                    <h3 data-i18n="app.title">Downloader Pro</h3>
                    <p id="app-version" data-i18n="app.version" data-i18n-options='{"version": "1.0.0"}'>Version 1.0.0</p>
                    <p data-i18n="about.description">Modern desktop application for downloading content from various platforms</p>
                    <div class="about-links">
                        <button class="link-button" onclick="electronAPI.openExternal('https://github.com/your-username/downloader-pro')" data-i18n="about.githubRepo">
                            GitHub Repository
                        </button>
                        <button class="link-button" onclick="electronAPI.openExternal('https://github.com/your-username/downloader-pro/issues')" data-i18n="about.reportIssues">
                            Report Issues
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Download History Modal -->
    <div id="history-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h2>📜 <span data-i18n="history.title">Download History</span></h2>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="history-controls">
                    <button id="clear-history" class="secondary-button">
                        <span class="icon">🗑️</span>
                        <span data-i18n="history.clearHistory">Clear History</span>
                    </button>
                    <button id="export-history" class="secondary-button">
                        <span class="icon">📤</span>
                        <span data-i18n="history.export">Export</span>
                    </button>
                </div>
                <div id="history-list" class="history-list">
                    <div class="empty-state">
                        <div class="empty-icon">📜</div>
                        <p data-i18n="history.empty.title">No download history yet</p>
                        <small data-i18n="history.empty.description">Completed downloads will appear here</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- i18next Scripts -->
    <script src="../../node_modules/i18next/dist/umd/i18next.min.js"></script>
    <script src="../../node_modules/i18next-http-backend/dist/umd/i18nextHttpBackend.min.js"></script>
    <script src="../../node_modules/i18next-browser-languagedetector/dist/umd/i18nextBrowserLanguageDetector.min.js"></script>

    <!-- Application Scripts -->
    <script src="../utils/format-helpers.js"></script>
    <script src="../utils/url-validator.js"></script>
    <script src="../utils/error-handler.js"></script>
    <script src="../utils/i18n.js"></script>
    <script src="js/app.js"></script>
    <script src="js/downloads.js"></script>
    <script src="js/settings.js"></script>
    <script src="js/themes.js"></script>
</body>
</html>
