<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https:;">
    <title>Downloader Pro</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/themes.css">
</head>
<body class="theme-light">
    <div id="app">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <div class="logo">
                    <img src="../../assets/icon.png" alt="Downloader Pro" class="logo-icon">
                    <h1 class="logo-text">Downloader Pro</h1>
                </div>
                <div class="header-controls">
                    <button id="theme-toggle" class="icon-button" title="Toggle Theme">
                        <span class="icon">🌙</span>
                    </button>
                    <button id="settings-button" class="icon-button" title="Settings">
                        <span class="icon">⚙️</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- URL Input Section -->
            <section class="url-input-section">
                <div class="input-container">
                    <div class="url-input-wrapper">
                        <input type="text" id="url-input" placeholder="Paste video URL here..." class="url-input">
                        <button id="paste-button" class="paste-button" title="Paste from clipboard">
                            <span class="icon">📋</span>
                        </button>
                    </div>
                    <div class="input-controls">
                        <label class="toggle-switch">
                            <input type="checkbox" id="auto-paste-toggle">
                            <span class="slider"></span>
                            <span class="toggle-label">Auto-detect clipboard</span>
                        </label>
                        <button id="download-button" class="download-button" disabled>
                            <span class="icon">⬇️</span>
                            <span class="text">Download</span>
                        </button>
                        <button id="schedule-button" class="schedule-button" onclick="window.app.showSchedulerModal()" title="Schedule Download">
                            <span class="icon">⏰</span>
                        </button>
                        <button id="notifications-button" class="notifications-button" onclick="window.app.showNotificationSettingsModal()" title="Notification Settings">
                            <span class="icon">🔔</span>
                        </button>
                        <button id="queue-management-button" class="queue-management-button" onclick="window.app.showQueueManagementModal()" title="Queue Management">
                            <span class="icon">⚡</span>
                        </button>
                    </div>
                </div>
            </section>

            <!-- Platform Icons Section -->
            <section class="platform-section">
                <div class="platform-header">
                    <h2>Find videos to download right in the app</h2>
                    <p>Tap the service icon below and start searching</p>
                </div>
                <div class="platform-icons">
                    <button class="platform-icon" data-platform="youtube" title="YouTube">
                        <img src="../../assets/platforms/youtube.png" alt="YouTube">
                        <span>YouTube</span>
                    </button>
                    <button class="platform-icon" data-platform="facebook" title="Facebook">
                        <img src="../../assets/platforms/facebook.png" alt="Facebook">
                        <span>Facebook</span>
                    </button>
                    <button class="platform-icon" data-platform="instagram" title="Instagram">
                        <img src="../../assets/platforms/instagram.png" alt="Instagram">
                        <span>Instagram</span>
                    </button>
                    <button class="platform-icon" data-platform="tiktok" title="TikTok">
                        <img src="../../assets/platforms/tiktok.png" alt="TikTok">
                        <span>TikTok</span>
                    </button>
                    <button class="platform-icon" data-platform="twitter" title="Twitter">
                        <img src="../../assets/platforms/twitter.png" alt="Twitter">
                        <span>Twitter</span>
                    </button>
                </div>
            </section>

            <!-- Video Preview Section -->
            <section id="video-preview" class="video-preview hidden">
                <div class="preview-card">
                    <div class="preview-thumbnail">
                        <img id="preview-image" src="" alt="Video thumbnail">
                        <div class="preview-duration" id="preview-duration"></div>
                    </div>
                    <div class="preview-info">
                        <h3 id="preview-title"></h3>
                        <p id="preview-description"></p>
                        <div class="preview-meta">
                            <span id="preview-uploader"></span>
                            <span id="preview-views"></span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Download Options Section -->
            <section id="download-options" class="download-options hidden">
                <div class="options-grid">
                    <div class="option-group">
                        <label for="download-type">Download Type</label>
                        <select id="download-type" class="select-input">
                            <option value="video">Video</option>
                            <option value="audio">Audio Only</option>
                            <option value="subtitles">Subtitles Only</option>
                            <option value="audio-tracks">Audio Tracks</option>
                        </select>
                    </div>
                    
                    <div class="option-group">
                        <label for="quality-select">Quality/Resolution</label>
                        <select id="quality-select" class="select-input">
                            <option value="best">Best Available</option>
                            <option value="8k">UHD 8K</option>
                            <option value="4k">HD 4K</option>
                            <option value="2k">HQ 2K</option>
                            <option value="1080p">1080p</option>
                            <option value="720p">720p</option>
                            <option value="480p">480p</option>
                            <option value="360p">360p</option>
                            <option value="240p">240p</option>
                            <option value="worst">Lowest Quality</option>
                        </select>
                    </div>
                    
                    <div class="option-group">
                        <label for="format-select">Output Format</label>
                        <select id="format-select" class="select-input">
                            <option value="auto">Auto</option>
                            <option value="mp4">MP4</option>
                            <option value="mkv">MKV</option>
                            <option value="webm">WebM</option>
                            <option value="mp3">MP3 (Audio)</option>
                            <option value="aac">AAC (Audio)</option>
                            <option value="ogg">OGG (Audio)</option>
                        </select>
                    </div>
                    
                    <div class="option-group">
                        <label for="destination-select">Destination</label>
                        <div class="destination-wrapper">
                            <select id="destination-select" class="select-input">
                                <option value="downloads">Downloads</option>
                                <option value="videos">Videos</option>
                                <option value="music">Music</option>
                                <option value="documents">Documents</option>
                                <option value="custom">Browse...</option>
                            </select>
                            <button id="browse-button" class="browse-button" title="Browse for folder">
                                <span class="icon">📁</span>
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Downloads Section -->
            <section class="downloads-section">
                <div class="section-header">
                    <h2>Downloads</h2>
                    <div class="download-controls">
                        <button id="clear-completed" class="secondary-button">Clear Completed</button>
                        <button id="pause-all" class="secondary-button">Pause All</button>
                        <button id="show-history" class="secondary-button">
                            <span class="icon">📜</span>
                            History
                        </button>
                    </div>
                </div>
                <div id="downloads-list" class="downloads-list">
                    <div class="empty-state">
                        <div class="empty-icon">📥</div>
                        <h3>No downloads yet</h3>
                        <p>Paste a video URL above to get started</p>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Modals -->
    <div id="settings-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Settings</h2>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <!-- Settings content will be loaded here -->
            </div>
        </div>
    </div>

    <div id="about-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h2>About Downloader Pro</h2>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="about-content">
                    <img src="../../assets/icon.png" alt="Downloader Pro" class="about-icon">
                    <h3>Downloader Pro</h3>
                    <p id="app-version">Version 1.0.0</p>
                    <p>Modern desktop application for downloading content from various platforms</p>
                    <div class="about-links">
                        <button class="link-button" onclick="electronAPI.openExternal('https://github.com/your-username/downloader-pro')">
                            GitHub Repository
                        </button>
                        <button class="link-button" onclick="electronAPI.openExternal('https://github.com/your-username/downloader-pro/issues')">
                            Report Issues
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Download History Modal -->
    <div id="history-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h2>📜 Download History</h2>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="history-controls">
                    <button id="clear-history" class="secondary-button">
                        <span class="icon">🗑️</span>
                        Clear History
                    </button>
                    <button id="export-history" class="secondary-button">
                        <span class="icon">📤</span>
                        Export
                    </button>
                </div>
                <div id="history-list" class="history-list">
                    <div class="empty-state">
                        <div class="empty-icon">📜</div>
                        <p>No download history yet</p>
                        <small>Completed downloads will appear here</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../utils/format-helpers.js"></script>
    <script src="../utils/url-validator.js"></script>
    <script src="../utils/error-handler.js"></script>
    <script src="js/app.js"></script>
    <script src="js/downloads.js"></script>
    <script src="js/settings.js"></script>
    <script src="js/themes.js"></script>
</body>
</html>
