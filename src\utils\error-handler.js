/**
 * Error handling utilities for Downloader Pro
 */

class ErrorHandler {
    constructor() {
        this.errorLog = [];
        this.maxLogSize = 100;
    }

    /**
     * Log an error with context
     * @param {Error|string} error - The error to log
     * @param {string} context - Context where the error occurred
     * @param {Object} metadata - Additional metadata
     */
    logError(error, context = 'Unknown', metadata = {}) {
        const errorEntry = {
            timestamp: new Date().toISOString(),
            context: context,
            message: error instanceof Error ? error.message : String(error),
            stack: error instanceof Error ? error.stack : null,
            metadata: metadata,
            id: this.generateErrorId()
        };

        this.errorLog.unshift(errorEntry);

        // Keep log size manageable
        if (this.errorLog.length > this.maxLogSize) {
            this.errorLog = this.errorLog.slice(0, this.maxLogSize);
        }

        // Log to console in development
        if (this.isDevelopment()) {
            console.error(`[${context}]`, error, metadata);
        }

        return errorEntry.id;
    }

    /**
     * Generate a unique error ID
     * @returns {string} - Unique error identifier
     */
    generateErrorId() {
        return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * Check if running in development mode
     * @returns {boolean} - True if in development
     */
    isDevelopment() {
        return process.env.NODE_ENV === 'development' || 
               (typeof window !== 'undefined' && window.location.hostname === 'localhost');
    }

    /**
     * Get user-friendly error message
     * @param {Error|string} error - The error
     * @param {string} fallback - Fallback message
     * @returns {string} - User-friendly error message
     */
    getUserFriendlyMessage(error, fallback = 'An unexpected error occurred') {
        if (!error) return fallback;

        const message = error instanceof Error ? error.message : String(error);

        // Map common errors to user-friendly messages
        const errorMappings = {
            'ENOTFOUND': 'Network connection error. Please check your internet connection.',
            'ECONNREFUSED': 'Unable to connect to the service. Please try again later.',
            'ETIMEDOUT': 'Request timed out. Please check your connection and try again.',
            'EACCES': 'Permission denied. Please check file permissions.',
            'ENOENT': 'File or directory not found.',
            'EMFILE': 'Too many files open. Please close some applications and try again.',
            'ENOMEM': 'Not enough memory available. Please close some applications.',
            'yt-dlp not found': 'yt-dlp is not installed. Please install it to enable downloads.',
            'Invalid URL': 'The provided URL is not valid or supported.',
            'Download failed': 'The download could not be completed. Please try again.',
            'Network error': 'Network connection error. Please check your internet connection.'
        };

        // Check for mapped errors
        for (const [key, friendlyMessage] of Object.entries(errorMappings)) {
            if (message.toLowerCase().includes(key.toLowerCase())) {
                return friendlyMessage;
            }
        }

        // Return original message if it's already user-friendly
        if (this.isUserFriendly(message)) {
            return message;
        }

        return fallback;
    }

    /**
     * Check if an error message is user-friendly
     * @param {string} message - Error message
     * @returns {boolean} - True if user-friendly
     */
    isUserFriendly(message) {
        const technicalTerms = [
            'ENOTFOUND', 'ECONNREFUSED', 'ETIMEDOUT', 'EACCES', 'ENOENT',
            'EMFILE', 'ENOMEM', 'TypeError', 'ReferenceError', 'SyntaxError',
            'spawn', 'SIGTERM', 'SIGKILL', 'stdout', 'stderr', 'errno'
        ];

        return !technicalTerms.some(term => 
            message.toLowerCase().includes(term.toLowerCase())
        );
    }

    /**
     * Handle download errors specifically
     * @param {Error} error - Download error
     * @param {string} url - URL that failed
     * @returns {string} - User-friendly message
     */
    handleDownloadError(error, url = '') {
        const message = error instanceof Error ? error.message : String(error);

        // Specific download error mappings
        const downloadErrorMappings = {
            'unsupported url': `This URL is not supported. Please try a different video URL.`,
            'video unavailable': 'This video is not available for download.',
            'private video': 'This video is private and cannot be downloaded.',
            'age restricted': 'This video is age-restricted and cannot be downloaded.',
            'geo blocked': 'This video is not available in your region.',
            'copyright': 'This video cannot be downloaded due to copyright restrictions.',
            'format not available': 'The requested quality/format is not available for this video.',
            'quota exceeded': 'Download quota exceeded. Please try again later.',
            'rate limited': 'Too many requests. Please wait a moment and try again.',
            'encoder not found': 'FFmpeg encoder not found. Please install FFmpeg or try downloading without conversion.',
            'postprocessing': 'Post-processing failed. Try downloading in original format or install FFmpeg.',
            'error opening output files': 'Cannot create output file. Check permissions and available disk space.',
            'ffmpeg': 'FFmpeg error occurred. Please ensure FFmpeg is properly installed.',
            'conversion failed': 'Format conversion failed. Try downloading in original format.'
        };

        for (const [key, friendlyMessage] of Object.entries(downloadErrorMappings)) {
            if (message.toLowerCase().includes(key)) {
                return friendlyMessage;
            }
        }

        return this.getUserFriendlyMessage(error, 'Download failed. Please try again.');
    }

    /**
     * Get recent errors
     * @param {number} limit - Maximum number of errors to return
     * @returns {Array} - Array of recent errors
     */
    getRecentErrors(limit = 10) {
        return this.errorLog.slice(0, limit);
    }

    /**
     * Clear error log
     */
    clearErrorLog() {
        this.errorLog = [];
    }

    /**
     * Get error statistics
     * @returns {Object} - Error statistics
     */
    getErrorStats() {
        const now = new Date();
        const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
        const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

        const recentErrors = this.errorLog.filter(error => 
            new Date(error.timestamp) > oneHourAgo
        );

        const dailyErrors = this.errorLog.filter(error => 
            new Date(error.timestamp) > oneDayAgo
        );

        const contextCounts = {};
        this.errorLog.forEach(error => {
            contextCounts[error.context] = (contextCounts[error.context] || 0) + 1;
        });

        return {
            total: this.errorLog.length,
            lastHour: recentErrors.length,
            lastDay: dailyErrors.length,
            byContext: contextCounts,
            mostCommon: Object.entries(contextCounts)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 5)
        };
    }

    /**
     * Export error log for debugging
     * @returns {string} - JSON string of error log
     */
    exportErrorLog() {
        return JSON.stringify({
            timestamp: new Date().toISOString(),
            errors: this.errorLog,
            stats: this.getErrorStats()
        }, null, 2);
    }

    /**
     * Handle unhandled promise rejections
     * @param {Event} event - Unhandled rejection event
     */
    handleUnhandledRejection(event) {
        this.logError(event.reason, 'UnhandledPromiseRejection', {
            type: 'unhandled_promise_rejection'
        });
    }

    /**
     * Handle uncaught exceptions
     * @param {Error} error - Uncaught exception
     */
    handleUncaughtException(error) {
        this.logError(error, 'UncaughtException', {
            type: 'uncaught_exception'
        });
    }

    /**
     * Setup global error handlers
     */
    setupGlobalHandlers() {
        if (typeof window !== 'undefined') {
            // Browser environment
            window.addEventListener('error', (event) => {
                this.logError(event.error || event.message, 'GlobalError', {
                    filename: event.filename,
                    lineno: event.lineno,
                    colno: event.colno
                });
            });

            window.addEventListener('unhandledrejection', (event) => {
                this.handleUnhandledRejection(event);
            });
        } else if (typeof process !== 'undefined' && process.on) {
            // Node.js environment
            try {
                process.on('uncaughtException', (error) => {
                    this.handleUncaughtException(error);
                });

                process.on('unhandledRejection', (reason, promise) => {
                    this.logError(reason, 'UnhandledPromiseRejection', {
                        promise: promise.toString()
                    });
                });
            } catch (error) {
                console.warn('Failed to setup Node.js error handlers:', error);
            }
        }
    }
}

// Initialize global error handler
if (typeof window !== 'undefined') {
    window.ErrorHandler = ErrorHandler;
    window.errorHandler = new ErrorHandler();
    window.errorHandler.setupGlobalHandlers();
}

// Create global instance
const errorHandler = new ErrorHandler();

// Setup global handlers
errorHandler.setupGlobalHandlers();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ErrorHandler;
    module.exports.errorHandler = errorHandler;
} else if (typeof window !== 'undefined') {
    window.ErrorHandler = ErrorHandler;
    window.errorHandler = errorHandler;
}
