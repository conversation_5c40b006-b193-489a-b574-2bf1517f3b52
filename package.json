{"name": "downloader-pro", "productName": "Downloader Pro", "version": "1.0.0", "description": "Modern desktop application for downloading content from various platforms", "main": "src/main.js", "homepage": "https://github.com/your-username/downloader-pro", "author": {"name": "Your Name", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build:win": "electron-builder --win", "build:mac": "electron-builder --mac", "build:linux": "electron-builder --linux", "dist": "npm run build", "install-ytdlp": "node scripts/install-ytdlp.js", "postinstall": "echo \"📋 To enable full functionality, run: npm run install-ytdlp\"", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["downloader", "video", "audio", "youtube", "facebook", "instagram", "tiktok", "twitter", "electron"], "devDependencies": {"electron": "^37.2.4", "electron-builder": "^26.0.12", "electron-reload": "^2.0.0-alpha.1", "electron-updater": "^6.6.2"}, "dependencies": {"child_process": "^1.0.2", "fs-extra": "^11.3.0", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "meilisearch": "^0.51.0", "node-fetch": "^3.3.2", "path-browserify": "^1.0.1", "util": "^0.12.5"}, "build": {"appId": "com.yourcompany.downloader-pro", "productName": "Downloader Pro", "directories": {"output": "dist"}, "files": ["src/**/*", "assets/**/*", "node_modules/**/*", "package.json"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": ["AppImage", "deb"], "icon": "assets/icon.png"}, "publish": {"provider": "github", "owner": "your-username", "repo": "downloader-pro"}}}